# pages/smart_login_page.py
"""
智能登录页面 - 展示如何使用智能元素定位
"""

from typing import Optional
from playwright.sync_api import Page
from pages.smart_page_base import SmartPageBase
from config import config
from utils import log

class SmartLoginPage(SmartPageBase):
    """
    智能登录页面
    
    支持传统选择器定义，失败时自动AI修复
    """
    
    def __init__(self, page: Page):
        super().__init__(page)
        self.url = config.base_url
    
    def _setup_elements(self):
        """定义页面元素"""
        
        # 用户名/邮箱输入框
        self.define_element(
            "username_input",
            description="用户名或邮箱输入框",
            primary_selector="input[name='email']",  # 基于实际页面的主选择器
            backup_selectors=[
                "#email",
                "input[type='email']",
                "input[placeholder*='邮箱']",
                "input[placeholder*='用户名']",
                ".email-input",
                "[data-testid='email-input']"
            ]
        )
        
        # 密码输入框
        self.define_element(
            "password_input", 
            description="密码输入框",
            primary_selector="input[name='password']",
            backup_selectors=[
                "#password",
                "input[type='password']",
                "input[placeholder*='密码']",
                ".password-input",
                "[data-testid='password-input']"
            ]
        )
        
        # 登录按钮
        self.define_element(
            "login_button",
            description="登录按钮",
            primary_selector="button[type='submit']",
            backup_selectors=[
                "#login-btn",
                ".login-btn",
                "button:has-text('登录')",
                "button:has-text('Login')",
                "input[value='登录']",
                "[data-testid='login-button']"
            ]
        )
        
        # 记住我复选框
        self.define_element(
            "remember_checkbox",
            description="记住我复选框",
            primary_selector="input[name='remember']",
            backup_selectors=[
                "#remember",
                "input[type='checkbox']",
                ".remember-checkbox",
                "[data-testid='remember-checkbox']"
            ]
        )
        
        # 忘记密码链接
        self.define_element(
            "forgot_password_link",
            description="忘记密码链接",
            primary_selector="a[href*='forgot']",
            backup_selectors=[
                ".forgot-password",
                "a:has-text('忘记密码')",
                "a:has-text('Forgot Password')",
                "[data-testid='forgot-password-link']"
            ]
        )
        
        # 错误提示信息
        self.define_element(
            "error_message",
            description="错误提示信息",
            primary_selector=".error-message",
            backup_selectors=[
                ".alert-danger",
                ".error",
                ".message.error",
                "[class*='error']",
                ".notification.is-danger"
            ]
        )
        
        # 成功提示信息
        self.define_element(
            "success_message",
            description="成功提示信息",
            primary_selector=".success-message",
            backup_selectors=[
                ".alert-success",
                ".success",
                ".message.success",
                "[class*='success']",
                ".notification.is-success"
            ]
        )
        
        # 加载指示器
        self.define_element(
            "loading_indicator",
            description="加载指示器",
            primary_selector=".loading",
            backup_selectors=[
                ".spinner",
                ".loader",
                "[class*='loading']",
                ".progress"
            ]
        )
    
    def navigate(self):
        """导航到登录页面"""
        log.info(f"导航到登录页面: {self.url}")
        self.page.goto(self.url)
        
        # 等待页面加载完成
        self.wait_for_element("username_input", timeout=10000)
    
    def login(self, email: str, password: str, remember: bool = False) -> bool:
        """
        执行登录操作
        
        Args:
            email: 邮箱地址
            password: 密码
            remember: 是否记住登录状态
            
        Returns:
            bool: 登录是否成功
        """
        log.info(f"开始登录: {email}")
        
        # 定义登录操作序列
        operations = [
            {"action": "fill", "element": "username_input", "value": email},
            {"action": "fill", "element": "password_input", "value": password}
        ]
        
        # 如果需要勾选记住我
        if remember:
            operations.append({"action": "click", "element": "remember_checkbox"})
        
        # 点击登录按钮
        operations.append({"action": "click", "element": "login_button"})
        
        # 执行批量操作
        result = self.batch_operations(operations)
        
        if result["success"] != result["total"]:
            log.error(f"登录操作失败: {result}")
            return False
        
        # 等待登录结果
        return self._wait_for_login_result()
    
    def _wait_for_login_result(self, timeout: float = 10000) -> bool:
        """等待登录结果"""
        import time
        start_time = time.time()
        
        while time.time() - start_time < timeout / 1000:
            # 检查是否有错误消息
            if self.is_element_visible("error_message"):
                error_msg = self.get_element_text("error_message")
                log.error(f"登录失败: {error_msg}")
                return False
            
            # 检查是否有成功消息或页面跳转
            if self.is_element_visible("success_message"):
                success_msg = self.get_element_text("success_message")
                log.info(f"登录成功: {success_msg}")
                return True
            
            # 检查URL是否发生变化（表示登录成功跳转）
            current_url = self.page.url
            if "login" not in current_url.lower():
                log.info("登录成功，页面已跳转")
                return True
            
            # 检查是否还在加载
            if self.is_element_visible("loading_indicator"):
                time.sleep(0.5)
                continue
            
            time.sleep(0.1)
        
        log.warning("登录结果等待超时")
        return False
    
    def quick_login(self) -> bool:
        """使用配置文件中的默认账户快速登录"""
        return self.login(config.email, config.password)
    
    def get_error_message(self) -> Optional[str]:
        """获取错误消息"""
        if self.is_element_visible("error_message"):
            return self.get_element_text("error_message")
        return None
    
    def get_success_message(self) -> Optional[str]:
        """获取成功消息"""
        if self.is_element_visible("success_message"):
            return self.get_element_text("success_message")
        return None
    
    def click_forgot_password(self) -> bool:
        """点击忘记密码链接"""
        return self.click_element("forgot_password_link")
    
    def is_login_form_visible(self) -> bool:
        """检查登录表单是否可见"""
        return (self.is_element_visible("username_input") and 
                self.is_element_visible("password_input") and
                self.is_element_visible("login_button"))
    
    def clear_form(self) -> bool:
        """清空登录表单"""
        operations = [
            {"action": "fill", "element": "username_input", "value": ""},
            {"action": "fill", "element": "password_input", "value": ""}
        ]
        
        result = self.batch_operations(operations)
        return result["success"] == result["total"]
    
    def validate_form_elements(self) -> Dict[str, bool]:
        """验证表单元素是否都可用"""
        elements_to_check = [
            "username_input",
            "password_input", 
            "login_button"
        ]
        
        validation_result = {}
        for element_name in elements_to_check:
            validation_result[element_name] = self.is_element_visible(element_name)
        
        return validation_result
    
    def get_login_page_health(self) -> Dict[str, Any]:
        """获取登录页面健康状况"""
        health = self.diagnose_page_health()
        
        # 添加登录页面特定的检查
        form_validation = self.validate_form_elements()
        all_elements_visible = all(form_validation.values())
        
        if not all_elements_visible:
            health["issues"].append("部分关键表单元素不可见")
            health["health_score"] -= 10
            health["recommendations"].append("检查登录表单的元素定位器")
        
        # 重新计算健康等级
        score = health["health_score"]
        if score >= 90:
            health["health_level"] = "优秀"
        elif score >= 75:
            health["health_level"] = "良好"
        elif score >= 60:
            health["health_level"] = "一般"
        else:
            health["health_level"] = "需要改进"
        
        health["form_validation"] = form_validation
        return health

# 使用示例和测试
if __name__ == "__main__":
    from playwright.sync_api import sync_playwright
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        # 创建智能登录页面
        login_page = SmartLoginPage(page)
        
        # 导航到登录页面
        login_page.navigate()
        
        # 验证页面元素
        validation = login_page.validate_form_elements()
        print(f"表单验证结果: {validation}")
        
        # 尝试登录
        success = login_page.quick_login()
        print(f"登录结果: {success}")
        
        if not success:
            error_msg = login_page.get_error_message()
            print(f"错误信息: {error_msg}")
        
        # 查看页面健康状况
        health = login_page.get_login_page_health()
        print(f"页面健康状况: {health['health_level']} ({health['health_score']}分)")
        
        if health["issues"]:
            print("发现的问题:")
            for issue in health["issues"]:
                print(f"  - {issue}")
        
        if health["recommendations"]:
            print("改进建议:")
            for rec in health["recommendations"]:
                print(f"  - {rec}")
        
        browser.close()
