# pages/__init__.py
"""
页面对象模块统一导入入口
提供简化的导入方式，如 README.md 中所示
"""

# 基础页面类
from .base_page import BasePage

# 核心页面类
from .login_page import LoginPage

# 导航器
try:
    from .manage_navigator import ManageNavigator
    from .adx_navigator import AdxNavigator
    from .put_navigator import PutNavigator
except ImportError:
    # 如果导航器不存在，提供占位符
    ManageNavigator = None
    AdxNavigator = None
    PutNavigator = None

# 管理页面
try:
    from .manage import (
        AccountListPage,
        CampaignListPage,
        PlanListPage,
        AccountReportPage,
        CampaignReportPage,
        PlanReportPage,
        TimeReportPage
    )
except ImportError:
    # 如果管理页面不存在，提供占位符
    AccountListPage = None
    CampaignListPage = None
    PlanListPage = None
    AccountReportPage = None
    CampaignReportPage = None
    PlanReportPage = None
    TimeReportPage = None

# 页面导航器（如果存在）
try:
    from .page_navigator import PageNavigator
except ImportError:
    PageNavigator = None

__all__ = [
    # 基础类
    "BasePage",
    "LoginPage",

    # 导航器
    "ManageNavigator",
    "AdxNavigator",
    "PutNavigator",
    "PageNavigator",

    # 管理页面
    "AccountListPage",
    "CampaignListPage",
    "PlanListPage",
    "AccountReportPage",
    "CampaignReportPage",
    "PlanReportPage",
    "TimeReportPage",
]