# pages/ai_enhanced_page.py
"""
AI增强的页面基类
提供智能元素定位和操作功能
"""

from typing import Optional, Dict, Any, List
from playwright.sync_api import Page, Locator
from pages.base_page import BasePage
from utils.ai_locator import AILocator
from utils import log
import time
import json

class AIEnhancedPage(BasePage):
    """AI增强的页面基类"""
    
    def __init__(self, page: Page):
        super().__init__(page)
        self.ai_locator = AILocator(page)
        self._element_cache = {}
        self._operation_history = []
        
    def find_element(self, description: str, context: str = "") -> Optional[Locator]:
        """
        智能查找元素
        
        Args:
            description: 元素描述，如"登录按钮"、"用户名输入框"
            context: 上下文信息，如"在登录表单中"
            
        Returns:
            Optional[Locator]: 找到的元素定位器
        """
        cache_key = f"{description}_{context}_{self.page.url}"
        
        # 检查缓存
        if cache_key in self._element_cache:
            cached_locator = self._element_cache[cache_key]
            try:
                if cached_locator.count() > 0:
                    log.debug(f"使用缓存定位器: {description}")
                    return cached_locator
            except Exception:
                # 缓存的定位器失效，清除缓存
                del self._element_cache[cache_key]
        
        # 使用AI智能定位
        locator = self.ai_locator.smart_locate(description, context)
        if locator:
            self._element_cache[cache_key] = locator
            log.info(f"智能定位成功: {description}")
        else:
            log.warning(f"智能定位失败: {description}")
            
        return locator
    
    def smart_click(self, description: str, context: str = "", timeout: float = 10000) -> bool:
        """
        智能点击元素
        
        Args:
            description: 元素描述
            context: 上下文信息
            timeout: 超时时间(毫秒)
            
        Returns:
            bool: 是否点击成功
        """
        try:
            element = self.find_element(description, context)
            if element:
                # 等待元素可点击
                element.wait_for(state="visible", timeout=timeout)
                element.click()
                
                # 记录操作历史
                self._record_operation("click", description, context, True)
                log.info(f"智能点击成功: {description}")
                return True
            else:
                self._record_operation("click", description, context, False, "元素未找到")
                return False
                
        except Exception as e:
            self._record_operation("click", description, context, False, str(e))
            log.error(f"智能点击失败: {description}, 错误: {e}")
            return False
    
    def smart_fill(self, description: str, value: str, context: str = "", timeout: float = 10000) -> bool:
        """
        智能填充输入框
        
        Args:
            description: 输入框描述
            value: 要填充的值
            context: 上下文信息
            timeout: 超时时间(毫秒)
            
        Returns:
            bool: 是否填充成功
        """
        try:
            element = self.find_element(description, context)
            if element:
                # 等待元素可编辑
                element.wait_for(state="visible", timeout=timeout)
                element.clear()
                element.fill(value)
                
                # 记录操作历史
                self._record_operation("fill", description, context, True, f"填充值: {value}")
                log.info(f"智能填充成功: {description} = {value}")
                return True
            else:
                self._record_operation("fill", description, context, False, "元素未找到")
                return False
                
        except Exception as e:
            self._record_operation("fill", description, context, False, str(e))
            log.error(f"智能填充失败: {description}, 错误: {e}")
            return False
    
    def smart_select(self, description: str, option: str, context: str = "", timeout: float = 10000) -> bool:
        """
        智能选择下拉框选项
        
        Args:
            description: 下拉框描述
            option: 要选择的选项
            context: 上下文信息
            timeout: 超时时间(毫秒)
            
        Returns:
            bool: 是否选择成功
        """
        try:
            element = self.find_element(description, context)
            if element:
                element.wait_for(state="visible", timeout=timeout)
                element.select_option(label=option)
                
                self._record_operation("select", description, context, True, f"选择选项: {option}")
                log.info(f"智能选择成功: {description} = {option}")
                return True
            else:
                self._record_operation("select", description, context, False, "元素未找到")
                return False
                
        except Exception as e:
            self._record_operation("select", description, context, False, str(e))
            log.error(f"智能选择失败: {description}, 错误: {e}")
            return False
    
    def smart_wait_for(self, description: str, context: str = "", timeout: float = 10000) -> bool:
        """
        智能等待元素出现
        
        Args:
            description: 元素描述
            context: 上下文信息
            timeout: 超时时间(毫秒)
            
        Returns:
            bool: 元素是否出现
        """
        try:
            element = self.find_element(description, context)
            if element:
                element.wait_for(state="visible", timeout=timeout)
                log.info(f"智能等待成功: {description}")
                return True
            else:
                log.warning(f"智能等待失败: {description} - 元素未找到")
                return False
                
        except Exception as e:
            log.error(f"智能等待超时: {description}, 错误: {e}")
            return False
    
    def smart_get_text(self, description: str, context: str = "") -> Optional[str]:
        """
        智能获取元素文本
        
        Args:
            description: 元素描述
            context: 上下文信息
            
        Returns:
            Optional[str]: 元素文本内容
        """
        try:
            element = self.find_element(description, context)
            if element:
                text = element.text_content()
                log.info(f"智能获取文本成功: {description} = {text}")
                return text
            else:
                log.warning(f"智能获取文本失败: {description} - 元素未找到")
                return None
                
        except Exception as e:
            log.error(f"智能获取文本失败: {description}, 错误: {e}")
            return None
    
    def smart_is_visible(self, description: str, context: str = "") -> bool:
        """
        智能检查元素是否可见
        
        Args:
            description: 元素描述
            context: 上下文信息
            
        Returns:
            bool: 元素是否可见
        """
        try:
            element = self.find_element(description, context)
            if element:
                visible = element.is_visible()
                log.debug(f"智能可见性检查: {description} = {visible}")
                return visible
            else:
                return False
                
        except Exception as e:
            log.error(f"智能可见性检查失败: {description}, 错误: {e}")
            return False
    
    def smart_hover(self, description: str, context: str = "") -> bool:
        """
        智能悬停元素
        
        Args:
            description: 元素描述
            context: 上下文信息
            
        Returns:
            bool: 是否悬停成功
        """
        try:
            element = self.find_element(description, context)
            if element:
                element.hover()
                self._record_operation("hover", description, context, True)
                log.info(f"智能悬停成功: {description}")
                return True
            else:
                self._record_operation("hover", description, context, False, "元素未找到")
                return False
                
        except Exception as e:
            self._record_operation("hover", description, context, False, str(e))
            log.error(f"智能悬停失败: {description}, 错误: {e}")
            return False
    
    def batch_operations(self, operations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批量执行智能操作
        
        Args:
            operations: 操作列表，每个操作包含type、description、context等
            
        Returns:
            Dict[str, Any]: 批量操作结果
        """
        results = {
            "total": len(operations),
            "success": 0,
            "failed": 0,
            "details": []
        }
        
        for i, operation in enumerate(operations):
            op_type = operation.get("type")
            description = operation.get("description", "")
            context = operation.get("context", "")
            
            try:
                if op_type == "click":
                    success = self.smart_click(description, context)
                elif op_type == "fill":
                    value = operation.get("value", "")
                    success = self.smart_fill(description, value, context)
                elif op_type == "select":
                    option = operation.get("option", "")
                    success = self.smart_select(description, option, context)
                elif op_type == "wait":
                    success = self.smart_wait_for(description, context)
                else:
                    success = False
                    log.warning(f"未知操作类型: {op_type}")
                
                if success:
                    results["success"] += 1
                else:
                    results["failed"] += 1
                
                results["details"].append({
                    "index": i,
                    "operation": operation,
                    "success": success
                })
                
            except Exception as e:
                results["failed"] += 1
                results["details"].append({
                    "index": i,
                    "operation": operation,
                    "success": False,
                    "error": str(e)
                })
                log.error(f"批量操作失败 [{i}]: {e}")
        
        log.info(f"批量操作完成: {results['success']}/{results['total']} 成功")
        return results
    
    def _record_operation(self, operation_type: str, description: str, context: str, 
                         success: bool, details: str = ""):
        """记录操作历史"""
        operation_record = {
            "timestamp": time.time(),
            "type": operation_type,
            "description": description,
            "context": context,
            "success": success,
            "details": details,
            "page_url": self.page.url
        }
        
        self._operation_history.append(operation_record)
        
        # 保持历史记录在合理范围内
        if len(self._operation_history) > 100:
            self._operation_history = self._operation_history[-50:]
    
    def get_operation_history(self) -> List[Dict[str, Any]]:
        """获取操作历史"""
        return self._operation_history.copy()
    
    def clear_cache(self):
        """清除元素缓存"""
        self._element_cache.clear()
        log.info("元素缓存已清除")
    
    def get_page_summary(self) -> Dict[str, Any]:
        """获取页面摘要信息"""
        try:
            return {
                "title": self.page.title(),
                "url": self.page.url,
                "cached_elements": len(self._element_cache),
                "operation_count": len(self._operation_history),
                "last_operation": self._operation_history[-1] if self._operation_history else None
            }
        except Exception as e:
            log.error(f"获取页面摘要失败: {e}")
            return {}

# 使用示例
if __name__ == "__main__":
    from playwright.sync_api import sync_playwright
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        
        # 创建AI增强页面实例
        ai_page = AIEnhancedPage(page)
        
        # 导航到测试页面
        page.goto("https://example.com")
        
        # 使用智能操作
        ai_page.smart_click("登录按钮", "在页面顶部")
        ai_page.smart_fill("用户名输入框", "<EMAIL>", "在登录表单中")
        ai_page.smart_fill("密码输入框", "password123", "在登录表单中")
        
        # 批量操作示例
        operations = [
            {"type": "fill", "description": "搜索框", "value": "测试关键词"},
            {"type": "click", "description": "搜索按钮"},
            {"type": "wait", "description": "搜索结果"}
        ]
        
        results = ai_page.batch_operations(operations)
        print(f"批量操作结果: {results}")
        
        browser.close()
