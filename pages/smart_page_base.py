# pages/smart_page_base.py
"""
智能页面基类 - 集成传统定位器和AI修复功能
"""

from typing import Dict, List, Optional, Any
from playwright.sync_api import Page
from pages.base_page import BasePage
from pages.smart_element import SmartElement, SmartElementFactory
from utils import log

class SmartPageBase(BasePage):
    """
    智能页面基类
    
    支持传统的元素定义方式，失败时自动使用AI修复
    """
    
    def __init__(self, page: Page):
        super().__init__(page)
        self.element_factory = SmartElementFactory(page)
        self._elements: Dict[str, SmartElement] = {}
        self._setup_elements()
    
    def _setup_elements(self):
        """
        子类重写此方法来定义页面元素
        
        示例:
        self.define_element(
            "login_button",
            description="登录按钮",
            primary_selector="button[type='submit']",
            backup_selectors=["#login-btn", ".login-button"]
        )
        """
        pass
    
    def define_element(self, name: str, description: str = "", 
                      primary_selector: str = "", backup_selectors: List[str] = None,
                      ai_enabled: bool = True) -> SmartElement:
        """
        定义页面元素
        
        Args:
            name: 元素名称
            description: 元素描述（用于AI定位）
            primary_selector: 主要选择器
            backup_selectors: 备用选择器列表
            ai_enabled: 是否启用AI修复
            
        Returns:
            SmartElement: 智能元素对象
        """
        element = self.element_factory.create_element(
            name=name,
            description=description,
            primary_selector=primary_selector,
            backup_selectors=backup_selectors,
            ai_enabled=ai_enabled
        )
        self._elements[name] = element
        
        # 动态添加为实例属性，方便访问
        setattr(self, name, element)
        
        log.debug(f"定义页面元素: {name} - {description}")
        return element
    
    def get_element(self, name: str) -> Optional[SmartElement]:
        """获取页面元素"""
        return self._elements.get(name)
    
    def click_element(self, name: str, **kwargs) -> bool:
        """点击指定元素"""
        element = self.get_element(name)
        if element:
            return element.click(**kwargs)
        else:
            log.error(f"元素未定义: {name}")
            return False
    
    def fill_element(self, name: str, value: str, **kwargs) -> bool:
        """填充指定元素"""
        element = self.get_element(name)
        if element:
            return element.fill(value, **kwargs)
        else:
            log.error(f"元素未定义: {name}")
            return False
    
    def wait_for_element(self, name: str, state: str = "visible", timeout: float = 30000) -> bool:
        """等待指定元素"""
        element = self.get_element(name)
        if element:
            return element.wait_for(state=state, timeout=timeout)
        else:
            log.error(f"元素未定义: {name}")
            return False
    
    def is_element_visible(self, name: str) -> bool:
        """检查元素是否可见"""
        element = self.get_element(name)
        if element:
            return element.is_visible()
        else:
            log.error(f"元素未定义: {name}")
            return False
    
    def get_element_text(self, name: str) -> Optional[str]:
        """获取元素文本"""
        element = self.get_element(name)
        if element:
            return element.text_content()
        else:
            log.error(f"元素未定义: {name}")
            return None
    
    def get_element_attribute(self, name: str, attribute: str) -> Optional[str]:
        """获取元素属性"""
        element = self.get_element(name)
        if element:
            return element.get_attribute(attribute)
        else:
            log.error(f"元素未定义: {name}")
            return None
    
    def batch_operations(self, operations: List[Dict[str, Any]]) -> Dict[str, Any]:
        """
        批量执行元素操作
        
        Args:
            operations: 操作列表
                [
                    {"action": "click", "element": "login_button"},
                    {"action": "fill", "element": "username", "value": "test"},
                    {"action": "wait", "element": "loading", "state": "hidden"}
                ]
                
        Returns:
            Dict: 执行结果
        """
        results = {
            "total": len(operations),
            "success": 0,
            "failed": 0,
            "details": []
        }
        
        for i, operation in enumerate(operations):
            action = operation.get("action")
            element_name = operation.get("element")
            
            try:
                success = False
                
                if action == "click":
                    success = self.click_element(element_name)
                elif action == "fill":
                    value = operation.get("value", "")
                    success = self.fill_element(element_name, value)
                elif action == "wait":
                    state = operation.get("state", "visible")
                    timeout = operation.get("timeout", 30000)
                    success = self.wait_for_element(element_name, state, timeout)
                elif action == "verify_visible":
                    success = self.is_element_visible(element_name)
                elif action == "verify_text":
                    expected_text = operation.get("expected_text", "")
                    actual_text = self.get_element_text(element_name)
                    success = expected_text in (actual_text or "")
                else:
                    log.warning(f"未知操作类型: {action}")
                
                if success:
                    results["success"] += 1
                else:
                    results["failed"] += 1
                
                results["details"].append({
                    "index": i,
                    "operation": operation,
                    "success": success
                })
                
            except Exception as e:
                results["failed"] += 1
                results["details"].append({
                    "index": i,
                    "operation": operation,
                    "success": False,
                    "error": str(e)
                })
                log.error(f"批量操作失败 [{i}]: {e}")
        
        log.info(f"批量操作完成: {results['success']}/{results['total']} 成功")
        return results
    
    def refresh_elements(self):
        """刷新所有元素缓存"""
        self.element_factory.clear_all_cache()
        log.info("已刷新所有元素缓存")
    
    def get_page_stats(self) -> Dict[str, Any]:
        """获取页面元素统计信息"""
        stats = self.element_factory.get_all_stats()
        
        # 计算总体统计
        total_stats = {
            "total_elements": len(stats),
            "total_attempts": 0,
            "total_successes": 0,
            "total_failures": 0,
            "ai_repairs": 0,
            "elements_with_issues": []
        }
        
        for element_name, element_stats in stats.items():
            total_stats["total_attempts"] += element_stats["total_attempts"]
            total_stats["total_successes"] += (
                element_stats["total_attempts"] - element_stats["total_failures"]
            )
            total_stats["total_failures"] += element_stats["total_failures"]
            total_stats["ai_repairs"] += element_stats["ai_success"]
            
            # 标记有问题的元素
            if element_stats["success_rate"] < 0.8:  # 成功率低于80%
                total_stats["elements_with_issues"].append({
                    "name": element_name,
                    "success_rate": element_stats["success_rate"],
                    "failures": element_stats["total_failures"]
                })
        
        # 计算总体成功率
        if total_stats["total_attempts"] > 0:
            total_stats["overall_success_rate"] = (
                total_stats["total_successes"] / total_stats["total_attempts"]
            )
        else:
            total_stats["overall_success_rate"] = 1.0
        
        return {
            "summary": total_stats,
            "elements": stats
        }
    
    def diagnose_page_health(self) -> Dict[str, Any]:
        """诊断页面健康状况"""
        stats = self.get_page_stats()
        summary = stats["summary"]
        
        health_score = 100
        issues = []
        recommendations = []
        
        # 检查总体成功率
        if summary["overall_success_rate"] < 0.9:
            health_score -= 20
            issues.append(f"总体成功率较低: {summary['overall_success_rate']:.2%}")
            recommendations.append("检查页面元素定位器是否需要更新")
        
        # 检查AI修复频率
        if summary["total_attempts"] > 0:
            ai_repair_rate = summary["ai_repairs"] / summary["total_attempts"]
            if ai_repair_rate > 0.3:  # AI修复率超过30%
                health_score -= 15
                issues.append(f"AI修复频率过高: {ai_repair_rate:.2%}")
                recommendations.append("更新主要选择器，减少对AI修复的依赖")
        
        # 检查问题元素
        if summary["elements_with_issues"]:
            health_score -= len(summary["elements_with_issues"]) * 5
            issues.append(f"发现 {len(summary['elements_with_issues'])} 个问题元素")
            recommendations.append("重新定义问题元素的选择器")
        
        # 确定健康等级
        if health_score >= 90:
            health_level = "优秀"
        elif health_score >= 75:
            health_level = "良好"
        elif health_score >= 60:
            health_level = "一般"
        else:
            health_level = "需要改进"
        
        return {
            "health_score": max(0, health_score),
            "health_level": health_level,
            "issues": issues,
            "recommendations": recommendations,
            "stats": stats
        }

# 使用示例：创建具体的页面类
class ExampleLoginPage(SmartPageBase):
    """示例登录页面"""
    
    def _setup_elements(self):
        """定义页面元素"""
        # 用户名输入框
        self.define_element(
            "username_input",
            description="用户名输入框",
            primary_selector="input[name='username']",
            backup_selectors=[
                "#username",
                "input[placeholder*='用户名']",
                "input[placeholder*='邮箱']",
                "input[type='email']"
            ]
        )
        
        # 密码输入框
        self.define_element(
            "password_input",
            description="密码输入框", 
            primary_selector="input[name='password']",
            backup_selectors=[
                "#password",
                "input[type='password']",
                "input[placeholder*='密码']"
            ]
        )
        
        # 登录按钮
        self.define_element(
            "login_button",
            description="登录按钮",
            primary_selector="button[type='submit']",
            backup_selectors=[
                "#login-btn",
                ".login-button",
                "button:has-text('登录')",
                "input[value='登录']"
            ]
        )
        
        # 错误消息
        self.define_element(
            "error_message",
            description="错误提示信息",
            primary_selector=".error-message",
            backup_selectors=[
                ".alert-danger",
                ".error",
                "[class*='error']"
            ]
        )
    
    def login(self, username: str, password: str) -> bool:
        """执行登录操作"""
        operations = [
            {"action": "fill", "element": "username_input", "value": username},
            {"action": "fill", "element": "password_input", "value": password},
            {"action": "click", "element": "login_button"}
        ]
        
        result = self.batch_operations(operations)
        success = result["success"] == result["total"]
        
        if success:
            log.info(f"登录操作完成: {username}")
        else:
            log.error(f"登录操作失败: {result}")
        
        return success
    
    def get_error_message(self) -> Optional[str]:
        """获取错误消息"""
        return self.get_element_text("error_message")

if __name__ == "__main__":
    from playwright.sync_api import sync_playwright

    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        page.goto("https://example.com/login")

        # 创建智能登录页面
        login_page = ExampleLoginPage(page)

        # 执行登录
        success = login_page.login("<EMAIL>", "password123")
        print(f"登录结果: {success}")

        # 查看页面健康状况
        health = login_page.diagnose_page_health()
        print(f"页面健康状况: {health}")

        browser.close()
