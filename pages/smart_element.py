# pages/smart_element.py
"""
智能元素定位器 - 支持传统定位器定义，失败后自动AI修复
"""

from typing import Dict, List, Optional, Union, Any
from playwright.sync_api import Page, Locator
from utils.ai_locator import AILocator
from utils import log
import time
import json
import hashlib

class SmartElement:
    """
    智能元素定位器
    
    支持传统的选择器定义，当定位失败时自动使用AI进行修复
    """
    
    def __init__(self, page: Page, name: str, description: str = "", 
                 primary_selector: str = "", backup_selectors: List[str] = None,
                 ai_enabled: bool = True):
        """
        初始化智能元素
        
        Args:
            page: Playwright页面对象
            name: 元素名称，用于日志和缓存
            description: 元素描述，用于AI定位
            primary_selector: 主要选择器
            backup_selectors: 备用选择器列表
            ai_enabled: 是否启用AI修复
        """
        self.page = page
        self.name = name
        self.description = description or name
        self.primary_selector = primary_selector
        self.backup_selectors = backup_selectors or []
        self.ai_enabled = ai_enabled
        
        # AI定位器（延迟初始化）
        self._ai_locator = None
        
        # 缓存相关
        self._cached_locator: Optional[Locator] = None
        self._cached_selector: Optional[str] = None
        self._cache_timestamp = 0
        self._cache_ttl = 30  # 缓存30秒
        
        # 统计信息
        self.stats = {
            "primary_success": 0,
            "backup_success": 0,
            "ai_success": 0,
            "total_failures": 0,
            "last_successful_selector": None
        }
    
    @property
    def ai_locator(self) -> AILocator:
        """延迟初始化AI定位器"""
        if self._ai_locator is None:
            self._ai_locator = AILocator(self.page)
        return self._ai_locator
    
    def locate(self, timeout: float = 10000) -> Optional[Locator]:
        """
        智能定位元素
        
        Args:
            timeout: 超时时间(毫秒)
            
        Returns:
            Optional[Locator]: 定位到的元素
        """
        # 1. 检查缓存
        if self._is_cache_valid():
            log.debug(f"使用缓存定位器: {self.name}")
            return self._cached_locator
        
        # 2. 尝试主要选择器
        if self.primary_selector:
            locator = self._try_selector(self.primary_selector, timeout)
            if locator:
                self._update_cache(locator, self.primary_selector)
                self.stats["primary_success"] += 1
                self.stats["last_successful_selector"] = self.primary_selector
                log.info(f"主选择器成功定位 {self.name}: {self.primary_selector}")
                return locator
        
        # 3. 尝试备用选择器
        for i, backup_selector in enumerate(self.backup_selectors):
            locator = self._try_selector(backup_selector, timeout)
            if locator:
                self._update_cache(locator, backup_selector)
                self.stats["backup_success"] += 1
                self.stats["last_successful_selector"] = backup_selector
                log.warning(f"备用选择器[{i}]成功定位 {self.name}: {backup_selector}")
                return locator
        
        # 4. AI修复定位
        if self.ai_enabled:
            log.warning(f"传统选择器失败，启用AI修复定位: {self.name}")
            locator = self._ai_repair_locate()
            if locator:
                self.stats["ai_success"] += 1
                log.info(f"AI修复成功定位 {self.name}")
                return locator
        
        # 5. 所有方法都失败
        self.stats["total_failures"] += 1
        log.error(f"所有定位方法都失败: {self.name}")
        return None
    
    def _try_selector(self, selector: str, timeout: float) -> Optional[Locator]:
        """尝试使用选择器定位元素"""
        try:
            locator = self.page.locator(selector)
            # 检查元素是否存在
            if locator.count() > 0:
                # 等待元素可见（可选）
                try:
                    locator.first.wait_for(state="attached", timeout=min(timeout, 3000))
                except:
                    pass  # 即使等待失败，如果元素存在也返回
                return locator
        except Exception as e:
            log.debug(f"选择器失败 {selector}: {e}")
        return None
    
    def _ai_repair_locate(self) -> Optional[Locator]:
        """使用AI修复定位"""
        try:
            # 使用AI定位器
            locator = self.ai_locator.smart_locate(self.description, "")
            if locator:
                # 获取AI生成的选择器（如果可能）
                ai_selector = self._extract_selector_from_locator(locator)
                if ai_selector:
                    self._update_cache(locator, ai_selector)
                    self.stats["last_successful_selector"] = f"AI:{ai_selector}"
                    
                    # 将成功的AI选择器添加到备用选择器中
                    if ai_selector not in self.backup_selectors:
                        self.backup_selectors.insert(0, ai_selector)
                        log.info(f"AI选择器已添加到备用列表: {ai_selector}")
                
                return locator
        except Exception as e:
            log.error(f"AI修复定位失败 {self.name}: {e}")
        return None
    
    def _extract_selector_from_locator(self, locator: Locator) -> Optional[str]:
        """从Locator对象中提取选择器字符串（如果可能）"""
        try:
            # 这是一个简化的实现，实际可能需要更复杂的逻辑
            # Playwright的Locator对象不直接暴露选择器字符串
            # 这里返回None，表示无法提取
            return None
        except:
            return None
    
    def _is_cache_valid(self) -> bool:
        """检查缓存是否有效"""
        if not self._cached_locator or not self._cached_selector:
            return False
        
        # 检查缓存时间
        if time.time() - self._cache_timestamp > self._cache_ttl:
            return False
        
        # 检查元素是否仍然存在
        try:
            return self._cached_locator.count() > 0
        except:
            return False
    
    def _update_cache(self, locator: Locator, selector: str):
        """更新缓存"""
        self._cached_locator = locator
        self._cached_selector = selector
        self._cache_timestamp = time.time()
    
    def clear_cache(self):
        """清除缓存"""
        self._cached_locator = None
        self._cached_selector = None
        self._cache_timestamp = 0
    
    # 代理Locator的常用方法
    def click(self, **kwargs) -> bool:
        """点击元素"""
        try:
            locator = self.locate()
            if locator:
                locator.click(**kwargs)
                log.info(f"点击成功: {self.name}")
                return True
            else:
                log.error(f"点击失败，元素未找到: {self.name}")
                return False
        except Exception as e:
            log.error(f"点击异常 {self.name}: {e}")
            return False
    
    def fill(self, value: str, **kwargs) -> bool:
        """填充文本"""
        try:
            locator = self.locate()
            if locator:
                locator.fill(value, **kwargs)
                log.info(f"填充成功 {self.name}: {value}")
                return True
            else:
                log.error(f"填充失败，元素未找到: {self.name}")
                return False
        except Exception as e:
            log.error(f"填充异常 {self.name}: {e}")
            return False
    
    def clear(self, **kwargs) -> bool:
        """清空内容"""
        try:
            locator = self.locate()
            if locator:
                locator.clear(**kwargs)
                log.info(f"清空成功: {self.name}")
                return True
            else:
                log.error(f"清空失败，元素未找到: {self.name}")
                return False
        except Exception as e:
            log.error(f"清空异常 {self.name}: {e}")
            return False
    
    def is_visible(self, **kwargs) -> bool:
        """检查是否可见"""
        try:
            locator = self.locate()
            if locator:
                return locator.is_visible(**kwargs)
            else:
                return False
        except Exception as e:
            log.debug(f"可见性检查异常 {self.name}: {e}")
            return False
    
    def wait_for(self, state: str = "visible", timeout: float = 30000) -> bool:
        """等待元素状态"""
        try:
            locator = self.locate()
            if locator:
                locator.wait_for(state=state, timeout=timeout)
                log.info(f"等待成功 {self.name}: {state}")
                return True
            else:
                log.error(f"等待失败，元素未找到: {self.name}")
                return False
        except Exception as e:
            log.error(f"等待异常 {self.name}: {e}")
            return False
    
    def text_content(self) -> Optional[str]:
        """获取文本内容"""
        try:
            locator = self.locate()
            if locator:
                return locator.text_content()
            else:
                return None
        except Exception as e:
            log.error(f"获取文本异常 {self.name}: {e}")
            return None
    
    def get_attribute(self, name: str) -> Optional[str]:
        """获取属性值"""
        try:
            locator = self.locate()
            if locator:
                return locator.get_attribute(name)
            else:
                return None
        except Exception as e:
            log.error(f"获取属性异常 {self.name}: {e}")
            return None
    
    def count(self) -> int:
        """获取元素数量"""
        try:
            locator = self.locate()
            if locator:
                return locator.count()
            else:
                return 0
        except Exception as e:
            log.error(f"计数异常 {self.name}: {e}")
            return 0
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        total_attempts = sum([
            self.stats["primary_success"],
            self.stats["backup_success"], 
            self.stats["ai_success"],
            self.stats["total_failures"]
        ])
        
        success_rate = 0
        if total_attempts > 0:
            success_attempts = total_attempts - self.stats["total_failures"]
            success_rate = success_attempts / total_attempts
        
        return {
            **self.stats,
            "total_attempts": total_attempts,
            "success_rate": success_rate,
            "cache_valid": self._is_cache_valid()
        }

class SmartElementFactory:
    """智能元素工厂类"""
    
    def __init__(self, page: Page):
        self.page = page
        self.elements: Dict[str, SmartElement] = {}
    
    def create_element(self, name: str, description: str = "", 
                      primary_selector: str = "", backup_selectors: List[str] = None,
                      ai_enabled: bool = True) -> SmartElement:
        """创建智能元素"""
        element = SmartElement(
            page=self.page,
            name=name,
            description=description,
            primary_selector=primary_selector,
            backup_selectors=backup_selectors,
            ai_enabled=ai_enabled
        )
        self.elements[name] = element
        return element
    
    def get_element(self, name: str) -> Optional[SmartElement]:
        """获取已创建的元素"""
        return self.elements.get(name)
    
    def clear_all_cache(self):
        """清除所有元素的缓存"""
        for element in self.elements.values():
            element.clear_cache()
    
    def get_all_stats(self) -> Dict[str, Dict[str, Any]]:
        """获取所有元素的统计信息"""
        return {name: element.get_stats() for name, element in self.elements.items()}

# 使用示例
if __name__ == "__main__":
    from playwright.sync_api import sync_playwright
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        page.goto("https://example.com")
        
        # 创建智能元素工厂
        factory = SmartElementFactory(page)
        
        # 定义智能元素
        login_button = factory.create_element(
            name="login_button",
            description="登录按钮",
            primary_selector="button[type='submit']",
            backup_selectors=[
                ".login-btn",
                "input[value='登录']",
                "button:has-text('登录')"
            ]
        )
        
        username_input = factory.create_element(
            name="username_input", 
            description="用户名输入框",
            primary_selector="input[name='username']",
            backup_selectors=[
                "#username",
                "input[placeholder*='用户名']",
                "input[type='email']"
            ]
        )
        
        # 使用智能元素
        if username_input.fill("<EMAIL>"):
            print("用户名填充成功")
        
        if login_button.click():
            print("登录按钮点击成功")
        
        # 查看统计信息
        stats = factory.get_all_stats()
        print(f"元素统计: {stats}")
        
        browser.close()
