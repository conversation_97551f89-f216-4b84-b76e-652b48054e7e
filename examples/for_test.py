from playwright.sync_api import sync_playwright
from config import config
from pages.login_page import LoginPage
from pages.manage_navigator import ManageNavigator
from pages.adx_navigator import AdxNavigator
# from utils import logger

email = config.email
password = config.password
with sync_playwright() as p:
    # 启动浏览器（示例使用Chromium）
    browser = p.chromium.launch(headless=False)
    context = browser.new_context(storage_state="auth.json")
    page = context.new_page()
    login_page = LoginPage(page)
    main_page = login_page.login(email,password)
    # account_page = promote_page.goto_account_page(config.account_name)
    # account_page.wait_for_element("text='投放管理'")
    # account_page.pause()
    # page.goto(config.base_url)
    # main_page = MainPage(page)
    # promote_page = main_page.goto_promote()
    # # promote_page.click("text='可用'")
    # # promote_page.pause()
    # # main_page.pause()
    # #  报表
    # report = main_page.goto_report()
    # report.goto_account_report()
    # report.pause()
    # report.goto_campaign_report()
    # report.goto_plan_report()
    # # # report.pause()
    # # table = report.locator(".row-table table")
    # # text = table.inner_text()
    # # log.debug(text)
    # # report.pause()
    # # report.goto_time_report()
    # #
    # # 账户
    # account = main_page.goto_account()
    # account.goto_account_manage()
    # account.goto_operate_log()
    # account.goto_qualification_manage()
    # account.pause()
    # 素材
    # assets = main_page.goto_assets()
    # assets.goto_app_manage()
    # assets.goto_rta_manage()
    # assets.goto_adx_ep_manage()
    # assets.goto_action_call_manage()
    # # assets.pause()
    #
    # financial = main_page.goto_financial()
    # financial.goto_revenue_report()
    # financial.goto_adx_cost()
    # financial.pause()
    #
    # toolbar = main_page.goto_toolbar()
    # toolbar.goto_data_monitor()
    #
    # traffic_tool = main_page.goto_traffic_tool()
    # traffic_tool.goto_traffic_center()
    # traffic_tool.goto_traffic_platform()
    # traffic_tool.goto_traffic_query()
    #
    # cps = main_page.goto_cps()
    # cps.goto_cps_account()
    # cps.goto_tracking_group()
    # cps.goto_ae_channel_report()



