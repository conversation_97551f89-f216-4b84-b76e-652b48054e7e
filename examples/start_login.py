from playwright.sync_api import sync_playwright
from config import config
from pages.login_page import LoginPage
from utils import log

email = config.email
password = config.password
with sync_playwright() as p:
    # 启动浏览器（示例使用Chromium）
    browser = p.chromium.launch(headless=False)
    context = browser.new_context()
    page = context.new_page()
    page.goto(config.base_url)
    page.wait_for_selector("text='登录'")
    login_page = LoginPage(page)
    main_page = login_page.login(email, password)
    # 等待两秒
    # page.wait_for_timeout(2000)

    context.storage_state(path="auth.json")
