# Pytest Fixture 编写指南

## 概述

本指南介绍如何使用项目中的pytest fixtures来编写高效、可维护的测试用例。

## 可用的Fixtures

### 1. 基础Fixtures

#### `browser` (session级别)
- **作用域**: session
- **功能**: 提供浏览器实例，整个测试会话共享
- **使用场景**: 通常不直接使用，由其他fixtures依赖

#### `context` (function级别)
- **作用域**: function
- **功能**: 为每个测试创建新的浏览器上下文
- **使用场景**: 需要隔离的浏览器环境

#### `page` (function级别)
- **作用域**: function
- **功能**: 为每个测试创建新的页面
- **使用场景**: 基础页面操作测试

```python
def test_basic_page_operation(page):
    page.goto("https://example.com")
    assert page.title() == "Example"
```

### 2. 页面Fixtures

#### `login_page`
- **功能**: 提供登录页面实例，已导航到登录页面
- **使用场景**: 登录相关测试

```python
def test_login_validation(login_page):
    login_page.input_email("<EMAIL>")
    login_page.input_password("wrongpassword")
    login_page.submit()
    assert login_page.get_error_message() != ""
```

#### `navigator`
- **功能**: 提供已登录的页面导航器
- **使用场景**: 需要登录后操作的测试
- **依赖**: login_page

```python
def test_navigation_after_login(navigator):
    campaign_page = navigator.navigate_to_campaign_list()
    assert campaign_page.table.is_visible()
```

#### `account_list_page`
- **功能**: 提供账户列表页面实例
- **使用场景**: 账户管理相关测试
- **依赖**: navigator

```python
def test_account_search(account_list_page):
    account_list_page.search_account("测试账户")
    count = account_list_page.get_account_count()
    assert count >= 0
```

#### `campaign_list_page`
- **功能**: 提供活动列表页面实例
- **使用场景**: 活动管理相关测试
- **依赖**: navigator

```python
def test_campaign_operations(campaign_list_page):
    campaign_list_page.search_campaign("测试活动")
    campaign_list_page.reset_search()
```

#### `account_report_page`
- **功能**: 提供账户报表页面实例
- **使用场景**: 报表查看相关测试
- **依赖**: navigator

```python
def test_report_data(account_report_page, date_range):
    account_report_page.set_date_range(
        date_range["start_date"], 
        date_range["end_date"]
    )
    account_report_page.query_report()
    data = account_report_page.get_report_data()
    assert isinstance(data, list)
```

### 3. 测试数据Fixtures

#### `test_campaign_data`
- **功能**: 提供测试活动数据
- **返回**: 包含活动信息的字典

```python
def test_create_campaign(campaign_list_page, test_campaign_data):
    campaign_list_page.create_campaign(
        test_campaign_data["name"],
        test_campaign_data["type"]
    )
```

#### `test_account_data`
- **功能**: 提供测试账户数据
- **返回**: 包含账户信息的字典

#### `date_range`
- **功能**: 提供默认日期范围（最近7天）
- **返回**: 包含start_date和end_date的字典

```python
def test_date_filter(account_report_page, date_range):
    account_report_page.set_date_range(
        date_range["start_date"],
        date_range["end_date"]
    )
```

## 编写测试用例的最佳实践

### 1. 选择合适的Fixture

根据测试需求选择最合适的fixture：

```python
# ❌ 过度使用高级fixture
def test_simple_login(navigator):  # 不需要导航器
    pass

# ✅ 使用合适的fixture
def test_simple_login(login_page):
    login_page.login("<EMAIL>", "password")
```

### 2. 组合使用Fixtures

```python
def test_comprehensive_workflow(campaign_list_page, test_campaign_data, date_range):
    # 创建活动
    campaign_list_page.create_campaign(
        test_campaign_data["name"],
        test_campaign_data["type"]
    )
    
    # 搜索活动
    campaign_list_page.search_campaign(test_campaign_data["name"])
    
    # 验证结果
    assert campaign_list_page.table.get_row_count() > 0
```

### 3. 使用Allure增强测试报告

```python
import allure

@allure.feature("活动管理")
@allure.story("活动创建")
def test_create_campaign(campaign_list_page, test_campaign_data):
    with allure.step("创建新活动"):
        campaign_list_page.create_campaign(
            test_campaign_data["name"],
            test_campaign_data["type"]
        )
    
    with allure.step("验证活动创建成功"):
        status = campaign_list_page.get_campaign_status(test_campaign_data["name"])
        assert status in ["启用", "暂停"]
```

### 4. 参数化测试

```python
@pytest.mark.parametrize("email,password,expected_error", [
    ("<EMAIL>", "password", "用户不存在"),
    ("<EMAIL>", "wrong", "密码错误"),
    ("", "password", "邮箱不能为空"),
])
def test_login_validation(login_page, email, password, expected_error):
    login_page.login_expect_failure(email, password)
    error_msg = login_page.get_error_message()
    assert expected_error in error_msg
```

### 5. 自定义Fixture

如果需要特定的测试数据或设置，可以创建自定义fixture：

```python
@pytest.fixture
def special_campaign_data():
    """特殊的活动数据"""
    return {
        "name": f"特殊活动_{int(time.time())}",
        "type": "展示推广",
        "budget": 5000,
        "target_audience": "18-35岁"
    }

def test_special_campaign(campaign_list_page, special_campaign_data):
    campaign_list_page.create_campaign(
        special_campaign_data["name"],
        special_campaign_data["type"]
    )
```

## 环境变量配置

可以通过环境变量控制测试行为：

```bash
# 设置浏览器类型
export BROWSER=firefox  # chromium(默认), firefox, webkit

# 设置无头模式
export HEADLESS=true    # false(默认), true

# 运行测试
pytest tests/test_login.py -v
```

## 常见测试模式

### 1. 登录测试
```python
def test_successful_login(login_page):
    navigator = login_page.login(config.email, config.password)
    assert navigator is not None

def test_failed_login(login_page):
    login_page.login_expect_failure("<EMAIL>", "wrongpass")
    assert login_page.get_error_message() != ""
```

### 2. 列表操作测试
```python
def test_list_operations(campaign_list_page):
    # 搜索
    campaign_list_page.search_campaign("测试")
    
    # 重置
    campaign_list_page.reset_search()
    
    # 获取数量
    count = campaign_list_page.table.get_row_count()
    assert count >= 0
```

### 3. 报表测试
```python
def test_report_generation(account_report_page, date_range):
    # 设置查询条件
    account_report_page.set_date_range(
        date_range["start_date"],
        date_range["end_date"]
    )
    
    # 查询数据
    account_report_page.query_report()
    
    # 验证结果
    data = account_report_page.get_report_data()
    assert len(data) >= 0
    
    # 导出报表
    account_report_page.export_report("Excel")
```

### 4. 状态管理测试
```python
def test_campaign_status_change(campaign_list_page):
    campaign_name = "测试活动"
    
    # 获取当前状态
    current_status = campaign_list_page.get_campaign_status(campaign_name)
    
    # 切换状态
    if current_status == "启用":
        campaign_list_page.pause_campaign(campaign_name)
        new_status = campaign_list_page.get_campaign_status(campaign_name)
        assert new_status == "暂停"
    else:
        campaign_list_page.enable_campaign(campaign_name)
        new_status = campaign_list_page.get_campaign_status(campaign_name)
        assert new_status == "启用"
```

## 调试技巧

### 1. 使用page.pause()调试
```python
def test_debug_example(page):
    page.goto("https://example.com")
    page.pause()  # 暂停执行，可以手动操作浏览器
```

### 2. 截图调试
```python
def test_with_screenshot(page):
    page.goto("https://example.com")
    page.screenshot(path="debug.png")
```

### 3. 查看页面内容
```python
def test_debug_content(page):
    page.goto("https://example.com")
    content = page.content()
    print(content)  # 输出页面HTML
```

通过合理使用这些fixtures和模式，可以编写出清晰、可维护、高效的自动化测试用例。 