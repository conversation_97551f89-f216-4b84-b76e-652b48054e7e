import pytest
from playwright.sync_api import expect
from utils import log
from pages.login_page import LoginPage
from pages.manage.campaign_list_page import CampaignListPage

class TestCampaignList:
    """
    活动列表页面测试类
    """
    
    @pytest.fixture(autouse=True)
    def setup(self, page):
        """
        测试前置条件：登录系统并进入活动列表页面
        """
        self.page = page
        self.login_page = LoginPage(page)
        self.campaign_list_page = CampaignListPage(page)
        
        # 登录系统
        self.login_page.navigate()
        self.login_page.login("admin", "password")
        
        # 导航到活动列表页面
        log.info("导航到活动列表页面")
        self.page.get_by_role("menuitem", name="推广").click()
        self.page.get_by_role("menuitem", name="活动管理").click()
        
        yield
        
        # 清理测试数据
        log.info("清理测试数据")
        self._cleanup_test_campaigns()
    
    def _cleanup_test_campaigns(self):
        """清理测试过程中创建的活动"""
        test_campaigns = ["测试活动1", "测试活动2"]
        for campaign in test_campaigns:
            if self.campaign_list_page.is_campaign_exist(campaign):
                try:
                    self.campaign_list_page.delete_campaign(campaign)
                except Exception as e:
                    log.error(f"清理测试活动 {campaign} 失败: {e}")
    
    def test_create_campaign(self):
        """测试创建新活动功能"""
        log.info("测试创建新活动功能")
        campaign_name = "测试活动1"
        
        # 创建活动
        self.campaign_list_page.create_campaign(campaign_name)
        
        # 验证活动是否创建成功
        assert self.campaign_list_page.is_campaign_exist(campaign_name), f"活动 {campaign_name} 创建失败"
        
        # 验证活动状态
        status = self.campaign_list_page.get_campaign_status(campaign_name)
        assert "活动中" in status, f"活动状态不正确，期望包含'活动中'，实际为: {status}"
    
    def test_pause_and_activate_campaign(self):
        """测试暂停和启用活动功能"""
        log.info("测试暂停和启用活动功能")
        campaign_name = "测试活动2"
        
        # 创建活动
        self.campaign_list_page.create_campaign(campaign_name)
        
        # 验证活动创建成功
        assert self.campaign_list_page.is_campaign_exist(campaign_name), f"活动 {campaign_name} 创建失败"
        
        # 暂停活动
        self.campaign_list_page.pause_campaign(campaign_name)
        
        # 验证活动状态变为暂停
        status = self.campaign_list_page.get_campaign_status(campaign_name)
        assert "已暂停" in status, f"活动状态不正确，期望包含'已暂停'，实际为: {status}"
        
        # 再次启用活动
        self.campaign_list_page.activate_campaign(campaign_name)
        
        # 验证活动状态变为活动中
        status = self.campaign_list_page.get_campaign_status(campaign_name)
        assert "活动中" in status, f"活动状态不正确，期望包含'活动中'，实际为: {status}"
    
    def test_search_campaign(self):
        """测试搜索活动功能"""
        log.info("测试搜索活动功能")
        campaign_name = "测试活动搜索"
        
        # 先创建一个用于搜索的活动
        self.campaign_list_page.create_campaign(campaign_name)
        
        # 重置搜索条件
        self.campaign_list_page.reset_search()
        
        # 搜索活动
        self.campaign_list_page.search_campaign(campaign_name)
        
        # 验证搜索结果包含创建的活动
        expect(self.page.get_by_role("cell").filter(has_text=campaign_name)).to_be_visible()
        
        # 清理：删除测试活动
        self.campaign_list_page.delete_campaign(campaign_name)
    
    def test_delete_campaign(self):
        """测试删除活动功能"""
        log.info("测试删除活动功能")
        campaign_name = "测试活动删除"
        
        # 先创建一个用于删除的活动
        self.campaign_list_page.create_campaign(campaign_name)
        
        # 验证活动创建成功
        assert self.campaign_list_page.is_campaign_exist(campaign_name), f"活动 {campaign_name} 创建失败"
        
        # 删除活动
        self.campaign_list_page.delete_campaign(campaign_name)
        
        # 验证活动已被删除
        assert not self.campaign_list_page.is_campaign_exist(campaign_name), f"活动 {campaign_name} 删除失败" 