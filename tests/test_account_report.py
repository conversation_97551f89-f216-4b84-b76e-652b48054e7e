import pytest
from pages.login_page import LoginPage
from pages.manage.account_report_page import AccountReportPage
from utils import log
from config import config

TEST_URL = config.base_url
TEST_EMAIL = config.email
TEST_PASSWORD = config.password


class TestAccountReport:
    def setup_method(self):
        # 在setup方法中不应该使用fixture参数
        pass
        
    @pytest.fixture(autouse=True)
    def setup(self, manage_page):
        """使用autouse fixture来设置必要的页面对象"""
        self.account_report_page = manage_page.goto_report().goto_account_report()
        
    def test_account_report_data_display(self):
        """测试账户报表数据是否正确显示"""
        # 验证报表中的数据元素是否存在
        assert self.account_report_page.has_report_data(), "账户报表数据未正确显示"
        # 获取并记录数据行数
        row_count = self.account_report_page.get_report_data_count()
        data_list = self.account_report_page.get_report_data()
        log.info(f"账户报表数据行数: {row_count}")
        assert row_count > 0, "账户报表没有数据行"
        log.info("账户报表数据: %s", data_list)

    # def test_take_screenshot(self, account_report_page):
    #     """测试截图功能"""
    #     # 确保页面加载完成
    #     assert account_report_page.is_page_loaded(), "账户报表页面加载失败"
    #     # 截取页面截图
    #     account_report_page.take_screenshot("reports/screenshots/account_report.png")
    #     log.info("账户报表页面截图保存成功") 