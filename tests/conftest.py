import pytest
import os
from playwright.sync_api import sync_playwright
from config import config
from pages import LoginPage, PageNavigator
from utils import log


@pytest.fixture(scope="session")
def browser():
    """会话级别的浏览器实例"""
    with sync_playwright() as p:
        # 从环境变量获取配置，提供合理默认值
        browser_type = os.getenv("BROWSER", "chromium")
        headless = os.getenv("HEADLESS", "false").lower() == "true"
        
        # 选择浏览器
        if browser_type == "firefox":
            browser_obj = p.firefox.launch(headless=headless)
        elif browser_type == "webkit":
            browser_obj = p.webkit.launch(headless=headless)
        else:
            browser_obj = p.chromium.launch(headless=headless)
            
        yield browser_obj
        browser_obj.close()


@pytest.fixture(scope="function")
def context(browser):
    """为每个测试创建新的浏览器上下文"""
    context = browser.new_context(
        viewport={"width": 1920, "height": 1080}
    )
    yield context
    context.close()


@pytest.fixture(scope="function")
def page(context):
    """为每个测试创建新的页面"""
    page = context.new_page()
    yield page
    page.close()


@pytest.fixture(scope="function")
def login_page(page):
    """登录页面fixture"""
    page.goto(config.base_url)
    return LoginPage(page)


@pytest.fixture(scope="function")
def navigator(login_page):
    """已登录的页面导航器"""
    navigator = login_page.login(config.email, config.password)
    if navigator is None:
        pytest.fail("登录失败")
    return navigator


# 页面级别的fixtures
@pytest.fixture(scope="function")
def account_list_page(navigator):
    """账户列表页面"""
    return navigator.navigate_to_account_list()


@pytest.fixture(scope="function")
def campaign_list_page(navigator):
    """活动列表页面"""
    return navigator.navigate_to_campaign_list()


@pytest.fixture(scope="function")
def account_report_page(navigator):
    """账户报表页面"""
    return navigator.navigate_to_account_report()


# 测试失败时自动截图
@pytest.hookimpl(tryfirst=True, hookwrapper=True)
def pytest_runtest_makereport(item, call):
    """测试失败时自动截图"""
    outcome = yield
    report = outcome.get_result()
    
    if report.when == "call" and report.failed:
        # 获取page对象
        page = item.funcargs.get("page")
        if page:
            # 创建截图目录
            screenshots_dir = "screenshots"
            os.makedirs(screenshots_dir, exist_ok=True)
            
            # 生成截图文件名
            test_name = item.nodeid.replace("/", "_").replace("::", "_")
            screenshot_path = os.path.join(screenshots_dir, f"fail_{test_name}.png")
            
            try:
                page.screenshot(path=screenshot_path)
                log.info(f"测试失败截图: {screenshot_path}")
                
                # 如果有allure，添加到报告中
                try:
                    import allure
                    with open(screenshot_path, "rb") as f:
                        allure.attach(
                            f.read(),
                            name="失败截图",
                            attachment_type=allure.attachment_type.PNG
                        )
                except ImportError:
                    pass
            except Exception as e:
                log.error(f"截图失败: {e}")


# 测试数据fixtures
@pytest.fixture
def test_campaign_data():
    """测试活动数据"""
    return {
        "name": "测试活动_自动化",
        "type": "搜索推广",
        "budget": 1000,
        "status": "启用"
    }


@pytest.fixture
def test_account_data():
    """测试账户数据"""
    return {
        "name": config.account_name,
        "status": "正常"
    }


@pytest.fixture
def date_range():
    """默认日期范围"""
    from datetime import datetime, timedelta
    end_date = datetime.now().strftime("%Y-%m-%d")
    start_date = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")
    return {"start_date": start_date, "end_date": end_date}
