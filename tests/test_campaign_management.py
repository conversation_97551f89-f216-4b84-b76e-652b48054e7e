import pytest
import allure
from pages import LoginPage, PageNavigator
from config import config


@allure.feature("活动管理")
class TestCampaignManagement:
    
    @pytest.fixture
    def navigator(self, page):
        """登录并返回页面导航器"""
        login_page = LoginPage(page)
        login_page.goto(config.base_url + "/login")
        navigator = login_page.login(config.email, config.password)
        assert navigator is not None, "登录失败"
        return navigator
    
    @allure.story("活动列表查看")
    def test_view_campaign_list(self, navigator):
        """测试查看活动列表"""
        with allure.step("导航到活动列表页面"):
            campaign_page = navigator.navigate_to_campaign_list()
            
        with allure.step("验证页面加载"):
            assert campaign_page.table.is_visible(), "活动列表表格应该可见"
            
        with allure.step("获取活动数量"):
            row_count = campaign_page.table.get_row_count()
            allure.attach(str(row_count), "活动数量", allure.attachment_type.TEXT)
            assert row_count >= 0, "活动数量应该大于等于0"
    
    @allure.story("活动搜索")
    def test_search_campaign(self, navigator):
        """测试活动搜索功能"""
        with allure.step("导航到活动列表页面"):
            campaign_page = navigator.navigate_to_campaign_list()
            
        with allure.step("搜索特定活动"):
            search_keyword = "测试活动"
            campaign_page.search_campaign(search_keyword)
            
        with allure.step("验证搜索结果"):
            # 这里可以添加具体的搜索结果验证
            pass
            
        with allure.step("重置搜索条件"):
            campaign_page.reset_search()
    
    @allure.story("活动状态管理")
    def test_campaign_status_management(self, navigator):
        """测试活动状态管理"""
        with allure.step("导航到活动列表页面"):
            campaign_page = navigator.navigate_to_campaign_list()
            
        # 这里需要根据实际的活动数据进行测试
        # 示例代码，实际使用时需要调整
        campaign_name = "示例活动"
        
        with allure.step(f"获取活动 {campaign_name} 的当前状态"):
            current_status = campaign_page.get_campaign_status(campaign_name)
            allure.attach(current_status, "当前状态", allure.attachment_type.TEXT)
            
        # 根据当前状态执行相应操作
        if current_status == "投放中":
            with allure.step("暂停活动"):
                campaign_page.pause_campaign(campaign_name)
                new_status = campaign_page.get_campaign_status(campaign_name)
                assert new_status == "已暂停", f"活动状态应该变为已暂停，实际为: {new_status}"
        elif current_status == "已暂停":
            with allure.step("启用活动"):
                campaign_page.enable_campaign(campaign_name)
                new_status = campaign_page.get_campaign_status(campaign_name)
                assert new_status == "投放中", f"活动状态应该变为投放中，实际为: {new_status}"


@allure.feature("报表查看")
class TestReportViewing:
    
    @pytest.fixture
    def navigator(self, page):
        """登录并返回页面导航器"""
        login_page = LoginPage(page)
        login_page.goto(config.base_url + "/login")
        navigator = login_page.login(config.email, config.password)
        assert navigator is not None, "登录失败"
        return navigator
    
    @allure.story("账户报表查看")
    def test_view_account_report(self, navigator):
        """测试查看账户报表"""
        with allure.step("导航到账户报表页面"):
            report_page = navigator.navigate_to_account_report()
            
        with allure.step("设置查询条件"):
            # 设置日期范围
            from datetime import datetime, timedelta
            end_date = datetime.now().strftime("%Y-%m-%d")
            start_date = (datetime.now() - timedelta(days=7)).strftime("%Y-%m-%d")
            report_page.set_date_range(start_date, end_date)
            
        with allure.step("查询报表数据"):
            report_page.query_report()
            
        with allure.step("验证报表数据"):
            report_data = report_page.get_report_data()
            assert isinstance(report_data, list), "报表数据应该是列表格式"
            
            # 获取汇总指标
            metrics = report_page.get_total_metrics()
            allure.attach(str(metrics), "汇总指标", allure.attachment_type.JSON)
            
        with allure.step("验证图表显示"):
            chart_loaded = report_page.verify_chart_data()
            assert chart_loaded, "图表应该正确加载并显示数据" 