# utils/ai_test_generator.py
"""
AI测试生成器
基于页面分析自动生成测试用例
"""

import json
import os
from typing import List, Dict, Any
from playwright.sync_api import Page
from utils import log

class AITestGenerator:
    """AI测试用例生成器"""
    
    def __init__(self, output_dir: str = "tests/generated"):
        self.output_dir = output_dir
        self._ensure_output_dir()
        
    def _ensure_output_dir(self):
        """确保输出目录存在"""
        os.makedirs(self.output_dir, exist_ok=True)
    
    def analyze_page_and_generate_tests(self, page: Page, page_name: str, 
                                      page_description: str = "") -> Dict[str, Any]:
        """
        分析页面并生成测试用例
        
        Args:
            page: Playwright页面对象
            page_name: 页面名称
            page_description: 页面功能描述
            
        Returns:
            Dict[str, Any]: 生成结果
        """
        log.info(f"开始分析页面并生成测试: {page_name}")
        
        # 1. 分析页面结构
        page_analysis = self._analyze_page_structure(page)
        
        # 2. 生成测试场景
        test_scenarios = self._generate_test_scenarios(page_analysis, page_description)
        
        # 3. 生成测试代码
        test_code = self._generate_test_code(page_name, test_scenarios, page_analysis)
        
        # 4. 生成页面对象代码
        page_object_code = self._generate_page_object_code(page_name, page_analysis)
        
        # 5. 保存生成的文件
        result = self._save_generated_files(page_name, test_code, page_object_code)
        
        log.info(f"测试生成完成: {page_name}")
        return result
    
    def _analyze_page_structure(self, page: Page) -> Dict[str, Any]:
        """分析页面结构"""
        try:
            analysis = {
                "title": page.title(),
                "url": page.url,
                "elements": {
                    "buttons": [],
                    "inputs": [],
                    "links": [],
                    "forms": [],
                    "tables": []
                }
            }
            
            # 分析按钮
            buttons = page.locator("button, input[type='button'], input[type='submit']").all()
            for i, button in enumerate(buttons[:10]):  # 限制数量
                try:
                    text = button.text_content() or button.get_attribute("value") or f"按钮{i+1}"
                    analysis["elements"]["buttons"].append({
                        "text": text.strip(),
                        "type": button.get_attribute("type") or "button",
                        "id": button.get_attribute("id") or "",
                        "class": button.get_attribute("class") or ""
                    })
                except Exception:
                    continue
            
            # 分析输入框
            inputs = page.locator("input[type='text'], input[type='email'], input[type='password'], textarea").all()
            for i, input_elem in enumerate(inputs[:10]):
                try:
                    placeholder = input_elem.get_attribute("placeholder") or f"输入框{i+1}"
                    analysis["elements"]["inputs"].append({
                        "placeholder": placeholder,
                        "type": input_elem.get_attribute("type") or "text",
                        "id": input_elem.get_attribute("id") or "",
                        "name": input_elem.get_attribute("name") or ""
                    })
                except Exception:
                    continue
            
            # 分析链接
            links = page.locator("a[href]").all()
            for i, link in enumerate(links[:10]):
                try:
                    text = link.text_content() or f"链接{i+1}"
                    href = link.get_attribute("href") or ""
                    analysis["elements"]["links"].append({
                        "text": text.strip(),
                        "href": href
                    })
                except Exception:
                    continue
            
            # 分析表单
            forms = page.locator("form").all()
            for i, form in enumerate(forms[:5]):
                try:
                    form_inputs = form.locator("input, textarea, select").count()
                    analysis["elements"]["forms"].append({
                        "index": i,
                        "input_count": form_inputs,
                        "action": form.get_attribute("action") or "",
                        "method": form.get_attribute("method") or "get"
                    })
                except Exception:
                    continue
            
            return analysis
            
        except Exception as e:
            log.error(f"页面结构分析失败: {e}")
            return {"title": "", "url": "", "elements": {}}
    
    def _generate_test_scenarios(self, page_analysis: Dict, description: str) -> List[Dict]:
        """生成测试场景"""
        scenarios = []
        elements = page_analysis.get("elements", {})
        
        # 基于页面元素生成基础测试场景
        
        # 1. 页面加载测试
        scenarios.append({
            "name": "test_page_load",
            "description": "测试页面是否正确加载",
            "type": "basic",
            "steps": [
                {"action": "navigate", "target": "页面"},
                {"action": "verify", "target": "页面标题", "expected": page_analysis.get("title", "")}
            ]
        })
        
        # 2. 表单测试（如果有表单）
        if elements.get("forms"):
            scenarios.append({
                "name": "test_form_submission",
                "description": "测试表单提交功能",
                "type": "form",
                "steps": []
            })
            
            # 为每个输入框生成填充步骤
            for input_elem in elements.get("inputs", []):
                scenarios[-1]["steps"].append({
                    "action": "fill",
                    "target": input_elem["placeholder"],
                    "value": self._generate_test_value(input_elem["type"])
                })
            
            # 添加提交步骤
            submit_buttons = [btn for btn in elements.get("buttons", []) 
                            if btn["type"] in ["submit", "button"] and 
                            any(word in btn["text"].lower() for word in ["提交", "登录", "注册", "确定", "submit", "login"])]
            
            if submit_buttons:
                scenarios[-1]["steps"].append({
                    "action": "click",
                    "target": submit_buttons[0]["text"]
                })
        
        # 3. 导航测试（如果有链接）
        if elements.get("links"):
            scenarios.append({
                "name": "test_navigation",
                "description": "测试页面导航功能",
                "type": "navigation",
                "steps": []
            })
            
            for link in elements.get("links", [])[:3]:  # 限制链接数量
                if link["href"] and not link["href"].startswith("javascript:"):
                    scenarios[-1]["steps"].append({
                        "action": "click",
                        "target": link["text"],
                        "verify": "页面跳转"
                    })
        
        # 4. 按钮交互测试
        interactive_buttons = [btn for btn in elements.get("buttons", []) 
                             if btn["type"] == "button" and btn["text"]]
        
        if interactive_buttons:
            scenarios.append({
                "name": "test_button_interactions",
                "description": "测试按钮交互功能",
                "type": "interaction",
                "steps": []
            })
            
            for button in interactive_buttons[:3]:
                scenarios[-1]["steps"].append({
                    "action": "click",
                    "target": button["text"],
                    "verify": "按钮响应"
                })
        
        return scenarios
    
    def _generate_test_value(self, input_type: str) -> str:
        """为不同类型的输入框生成测试值"""
        test_values = {
            "text": "测试文本",
            "email": "<EMAIL>",
            "password": "Test123!",
            "number": "123",
            "tel": "13800138000",
            "url": "https://example.com"
        }
        return test_values.get(input_type, "测试值")
    
    def _generate_test_code(self, page_name: str, scenarios: List[Dict], 
                          page_analysis: Dict) -> str:
        """生成测试代码"""
        class_name = f"Test{page_name.replace('_', '').title()}"
        
        code = f'''# tests/generated/test_{page_name}.py
"""
自动生成的测试文件 - {page_name}
基于页面分析自动生成，可根据需要进行调整
"""

import pytest
import allure
from pages.ai_enhanced_page import AIEnhancedPage
from config import config

@allure.feature("{page_name}页面测试")
class {class_name}:
    """
    {page_name}页面自动生成的测试类
    页面URL: {page_analysis.get('url', '')}
    """
    
    @pytest.fixture(autouse=True)
    def setup(self, page):
        """测试前置设置"""
        self.ai_page = AIEnhancedPage(page)
        self.page_url = "{page_analysis.get('url', '')}"
        
'''
        
        # 为每个场景生成测试方法
        for scenario in scenarios:
            code += f'''
    @allure.story("{scenario['description']}")
    def {scenario['name']}(self):
        """
        {scenario['description']}
        测试类型: {scenario['type']}
        """
        with allure.step("导航到页面"):
            self.ai_page.page.goto(self.page_url)
            
'''
            
            # 生成测试步骤
            for step in scenario.get('steps', []):
                if step['action'] == 'fill':
                    code += f'''        with allure.step("填充{step['target']}"):
            assert self.ai_page.smart_fill("{step['target']}", "{step['value']}"), "填充失败"
            
'''
                elif step['action'] == 'click':
                    code += f'''        with allure.step("点击{step['target']}"):
            assert self.ai_page.smart_click("{step['target']}"), "点击失败"
            
'''
                elif step['action'] == 'verify':
                    if step['target'] == '页面标题':
                        code += f'''        with allure.step("验证页面标题"):
            title = self.ai_page.page.title()
            assert "{step['expected']}" in title, f"页面标题验证失败: {{title}}"
            
'''
        
        return code
    
    def _generate_page_object_code(self, page_name: str, page_analysis: Dict) -> str:
        """生成页面对象代码"""
        class_name = f"{page_name.replace('_', '').title()}Page"
        
        code = f'''# pages/generated/{page_name}_page.py
"""
自动生成的页面对象 - {page_name}
基于页面分析自动生成，可根据需要进行调整
"""

from pages.ai_enhanced_page import AIEnhancedPage
from playwright.sync_api import Page
from utils import log

class {class_name}(AIEnhancedPage):
    """
    {page_name}页面对象
    页面URL: {page_analysis.get('url', '')}
    """
    
    def __init__(self, page: Page):
        super().__init__(page)
        self.page_url = "{page_analysis.get('url', '')}"
    
    def navigate(self):
        """导航到页面"""
        self.page.goto(self.page_url)
        log.info(f"导航到{page_name}页面")
    
'''
        
        # 为按钮生成方法
        for button in page_analysis.get('elements', {}).get('buttons', []):
            method_name = self._to_method_name(button['text'])
            code += f'''    def click_{method_name}(self):
        """点击{button['text']}"""
        return self.smart_click("{button['text']}")
    
'''
        
        # 为输入框生成方法
        for input_elem in page_analysis.get('elements', {}).get('inputs', []):
            method_name = self._to_method_name(input_elem['placeholder'])
            code += f'''    def fill_{method_name}(self, value: str):
        """填充{input_elem['placeholder']}"""
        return self.smart_fill("{input_elem['placeholder']}", value)
    
'''
        
        return code
    
    def _to_method_name(self, text: str) -> str:
        """将文本转换为方法名"""
        # 简单的转换逻辑
        import re
        # 移除特殊字符，保留中英文和数字
        clean_text = re.sub(r'[^\w\u4e00-\u9fff]', '_', text)
        # 移除多余的下划线
        clean_text = re.sub(r'_+', '_', clean_text).strip('_')
        return clean_text.lower()
    
    def _save_generated_files(self, page_name: str, test_code: str, 
                            page_object_code: str) -> Dict[str, Any]:
        """保存生成的文件"""
        result = {
            "page_name": page_name,
            "files_generated": [],
            "success": True,
            "errors": []
        }
        
        try:
            # 保存测试文件
            test_file_path = os.path.join(self.output_dir, f"test_{page_name}.py")
            with open(test_file_path, 'w', encoding='utf-8') as f:
                f.write(test_code)
            result["files_generated"].append(test_file_path)
            
            # 保存页面对象文件
            pages_dir = os.path.join(os.path.dirname(self.output_dir), "pages", "generated")
            os.makedirs(pages_dir, exist_ok=True)
            page_file_path = os.path.join(pages_dir, f"{page_name}_page.py")
            with open(page_file_path, 'w', encoding='utf-8') as f:
                f.write(page_object_code)
            result["files_generated"].append(page_file_path)
            
            log.info(f"成功生成文件: {result['files_generated']}")
            
        except Exception as e:
            result["success"] = False
            result["errors"].append(str(e))
            log.error(f"保存生成文件失败: {e}")
        
        return result

# 使用示例
if __name__ == "__main__":
    from playwright.sync_api import sync_playwright
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        page.goto("https://example.com/login")
        
        generator = AITestGenerator()
        result = generator.analyze_page_and_generate_tests(
            page, 
            "login", 
            "用户登录页面"
        )
        
        print(f"生成结果: {result}")
        browser.close()
