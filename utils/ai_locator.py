# utils/ai_locator.py
"""
AI增强的智能定位器系统
提供基于自然语言描述的智能元素定位功能
"""

import json
import time
from typing import List, Dict, Optional, Union, Any
from playwright.sync_api import Page, Locator
from utils import log
import hashlib
import os

class AILocatorStrategy:
    """定位策略类"""
    
    def __init__(self, strategy_type: str, selector: str, confidence: float = 0.5):
        self.strategy_type = strategy_type
        self.selector = selector
        self.confidence = confidence
        self.success_count = 0
        self.failure_count = 0
    
    @property
    def success_rate(self) -> float:
        """计算成功率"""
        total = self.success_count + self.failure_count
        return self.success_count / total if total > 0 else 0.5
    
    def record_success(self):
        """记录成功"""
        self.success_count += 1
    
    def record_failure(self):
        """记录失败"""
        self.failure_count += 1

class AILocator:
    """AI增强的智能定位器"""
    
    def __init__(self, page: Page, cache_dir: str = "data/ai_cache"):
        self.page = page
        self.cache_dir = cache_dir
        self._ensure_cache_dir()
        self.strategy_cache = self._load_strategy_cache()
        
    def _ensure_cache_dir(self):
        """确保缓存目录存在"""
        os.makedirs(self.cache_dir, exist_ok=True)
    
    def _load_strategy_cache(self) -> Dict:
        """加载策略缓存"""
        cache_file = os.path.join(self.cache_dir, "locator_strategies.json")
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                log.warning(f"加载策略缓存失败: {e}")
        return {}
    
    def _save_strategy_cache(self):
        """保存策略缓存"""
        cache_file = os.path.join(self.cache_dir, "locator_strategies.json")
        try:
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.strategy_cache, f, ensure_ascii=False, indent=2)
        except Exception as e:
            log.warning(f"保存策略缓存失败: {e}")
    
    def smart_locate(self, description: str, context: str = "") -> Optional[Locator]:
        """
        基于自然语言描述智能定位元素
        
        Args:
            description: 元素描述，如"登录按钮"、"用户名输入框"
            context: 上下文信息，如"在登录表单中"
            
        Returns:
            Optional[Locator]: 定位到的元素，如果找不到返回None
        """
        log.info(f"智能定位元素: {description} (上下文: {context})")
        
        # 1. 生成缓存键
        cache_key = self._generate_cache_key(description, context)
        
        # 2. 尝试使用缓存的策略
        if cache_key in self.strategy_cache:
            cached_strategies = self.strategy_cache[cache_key]
            for strategy_data in sorted(cached_strategies, key=lambda x: x.get('success_rate', 0), reverse=True):
                try:
                    locator = self._try_selector(strategy_data['selector'])
                    if locator and locator.count() > 0:
                        self._update_strategy_success(cache_key, strategy_data['selector'])
                        log.info(f"使用缓存策略成功定位: {strategy_data['selector']}")
                        return locator
                except Exception:
                    self._update_strategy_failure(cache_key, strategy_data['selector'])
                    continue
        
        # 3. 生成新的定位策略
        strategies = self._generate_locator_strategies(description, context)
        
        # 4. 尝试每个策略
        for strategy in strategies:
            try:
                locator = self._try_selector(strategy.selector)
                if locator and locator.count() > 0:
                    self._cache_successful_strategy(cache_key, strategy)
                    log.info(f"新策略成功定位: {strategy.selector}")
                    return locator
            except Exception as e:
                log.debug(f"策略失败 {strategy.selector}: {e}")
                continue
        
        # 5. 如果所有策略都失败，尝试AI分析页面
        return self._ai_fallback_locate(description, context)
    
    def _generate_cache_key(self, description: str, context: str) -> str:
        """生成缓存键"""
        content = f"{description}_{context}_{self.page.url}"
        return hashlib.md5(content.encode()).hexdigest()
    
    def _generate_locator_strategies(self, description: str, context: str) -> List[AILocatorStrategy]:
        """生成定位策略列表"""
        strategies = []
        
        # 基于描述生成不同类型的选择器
        keywords = self._extract_keywords(description)
        
        # 1. Role-based策略
        if "按钮" in description or "button" in description.lower():
            strategies.append(AILocatorStrategy("role", f'role=button[name*="{keywords[0]}"]', 0.9))
            strategies.append(AILocatorStrategy("role", f'role=button >> text="{keywords[0]}"', 0.8))
        
        if "输入" in description or "input" in description.lower():
            strategies.append(AILocatorStrategy("role", f'role=textbox[name*="{keywords[0]}"]', 0.9))
            strategies.append(AILocatorStrategy("role", f'input[placeholder*="{keywords[0]}"]', 0.8))
        
        # 2. Text-based策略
        for keyword in keywords:
            strategies.append(AILocatorStrategy("text", f'text="{keyword}"', 0.7))
            strategies.append(AILocatorStrategy("text", f'text*="{keyword}"', 0.6))
        
        # 3. CSS选择器策略
        for keyword in keywords:
            strategies.append(AILocatorStrategy("css", f'[data-testid*="{keyword.lower()}"]', 0.8))
            strategies.append(AILocatorStrategy("css", f'.{keyword.lower()}-btn', 0.5))
            strategies.append(AILocatorStrategy("css", f'#{keyword.lower()}-input', 0.5))
        
        # 4. 组合策略
        if context:
            context_keywords = self._extract_keywords(context)
            for ctx_keyword in context_keywords:
                for keyword in keywords:
                    strategies.append(AILocatorStrategy(
                        "combined", 
                        f'[class*="{ctx_keyword.lower()}"] >> text="{keyword}"', 
                        0.6
                    ))
        
        # 按置信度排序
        strategies.sort(key=lambda x: x.confidence, reverse=True)
        return strategies
    
    def _extract_keywords(self, text: str) -> List[str]:
        """从描述中提取关键词"""
        # 简单的关键词提取，实际可以使用更复杂的NLP
        keywords = []
        
        # 移除常见的修饰词
        stop_words = {"的", "在", "中", "按钮", "输入框", "文本", "链接", "图片", "表单"}
        words = text.replace("，", " ").replace("。", " ").split()
        
        for word in words:
            if word not in stop_words and len(word) > 1:
                keywords.append(word)
        
        return keywords[:3]  # 最多返回3个关键词
    
    def _try_selector(self, selector: str) -> Optional[Locator]:
        """尝试使用选择器定位元素"""
        try:
            if selector.startswith('role='):
                # 处理role选择器
                role_part = selector[5:]
                if '[name*=' in role_part:
                    role, name_part = role_part.split('[name*=', 1)
                    name = name_part.rstrip('"]')
                    return self.page.get_by_role(role, name=name)
                elif ' >> text=' in role_part:
                    role, text_part = role_part.split(' >> text=', 1)
                    text = text_part.strip('"')
                    return self.page.get_by_role(role).filter(has_text=text)
                else:
                    return self.page.get_by_role(role_part)
            elif selector.startswith('text='):
                text = selector[5:].strip('"')
                return self.page.get_by_text(text, exact=True)
            elif selector.startswith('text*='):
                text = selector[6:].strip('"')
                return self.page.get_by_text(text, exact=False)
            else:
                return self.page.locator(selector)
        except Exception as e:
            log.debug(f"选择器 {selector} 执行失败: {e}")
            return None
    
    def _cache_successful_strategy(self, cache_key: str, strategy: AILocatorStrategy):
        """缓存成功的策略"""
        if cache_key not in self.strategy_cache:
            self.strategy_cache[cache_key] = []
        
        strategy_data = {
            "selector": strategy.selector,
            "strategy_type": strategy.strategy_type,
            "confidence": strategy.confidence,
            "success_count": 1,
            "failure_count": 0,
            "success_rate": 1.0,
            "last_used": time.time()
        }
        
        self.strategy_cache[cache_key].append(strategy_data)
        self._save_strategy_cache()
    
    def _update_strategy_success(self, cache_key: str, selector: str):
        """更新策略成功记录"""
        if cache_key in self.strategy_cache:
            for strategy in self.strategy_cache[cache_key]:
                if strategy['selector'] == selector:
                    strategy['success_count'] += 1
                    total = strategy['success_count'] + strategy['failure_count']
                    strategy['success_rate'] = strategy['success_count'] / total
                    strategy['last_used'] = time.time()
                    break
        self._save_strategy_cache()
    
    def _update_strategy_failure(self, cache_key: str, selector: str):
        """更新策略失败记录"""
        if cache_key in self.strategy_cache:
            for strategy in self.strategy_cache[cache_key]:
                if strategy['selector'] == selector:
                    strategy['failure_count'] += 1
                    total = strategy['success_count'] + strategy['failure_count']
                    strategy['success_rate'] = strategy['success_count'] / total
                    break
        self._save_strategy_cache()
    
    def _ai_fallback_locate(self, description: str, context: str) -> Optional[Locator]:
        """AI兜底定位策略"""
        log.warning(f"常规策略失败，尝试AI分析定位: {description}")
        
        # 这里可以集成真正的AI模型
        # 目前使用启发式方法作为示例
        
        # 1. 分析页面结构
        page_structure = self._analyze_current_page()
        
        # 2. 基于页面结构生成新策略
        fallback_strategies = self._generate_fallback_strategies(description, page_structure)
        
        # 3. 尝试兜底策略
        for strategy in fallback_strategies:
            try:
                locator = self._try_selector(strategy)
                if locator and locator.count() > 0:
                    log.info(f"AI兜底策略成功: {strategy}")
                    return locator
            except Exception:
                continue
        
        log.error(f"无法定位元素: {description}")
        return None
    
    def _analyze_current_page(self) -> Dict:
        """分析当前页面结构"""
        try:
            # 获取页面基本信息
            title = self.page.title()
            url = self.page.url
            
            # 获取主要元素统计
            buttons = self.page.locator("button").count()
            inputs = self.page.locator("input").count()
            links = self.page.locator("a").count()
            
            return {
                "title": title,
                "url": url,
                "elements": {
                    "buttons": buttons,
                    "inputs": inputs,
                    "links": links
                }
            }
        except Exception as e:
            log.warning(f"页面分析失败: {e}")
            return {}
    
    def _generate_fallback_strategies(self, description: str, page_structure: Dict) -> List[str]:
        """生成兜底策略"""
        strategies = []
        
        # 基于页面结构生成更通用的选择器
        if "按钮" in description:
            strategies.extend([
                "button",
                "[type='button']",
                "[type='submit']",
                ".btn",
                ".button"
            ])
        
        if "输入" in description:
            strategies.extend([
                "input[type='text']",
                "input[type='email']",
                "input[type='password']",
                "textarea",
                ".input"
            ])
        
        return strategies

# 使用示例
if __name__ == "__main__":
    from playwright.sync_api import sync_playwright
    
    with sync_playwright() as p:
        browser = p.chromium.launch(headless=False)
        page = browser.new_page()
        page.goto("https://example.com")
        
        ai_locator = AILocator(page)
        
        # 智能定位示例
        login_button = ai_locator.smart_locate("登录按钮", "在页面顶部")
        if login_button:
            print(f"找到登录按钮: {login_button}")
        
        browser.close()
